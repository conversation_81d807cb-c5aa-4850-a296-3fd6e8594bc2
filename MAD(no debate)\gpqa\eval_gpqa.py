import json
import eval_example
def load_json(file_path):
    with open(file_path) as f:
        data = json.load(f)
    return data

    
def weighted_answer_sampling(answer, accuracy):
    """
    根据persona的准确率对答案进行加权采样
    
    Args:
        answer (list): 按顺序的persona回答答案
        accuracy (list): 按顺序的persona准确率
    
    Returns:
        str: 最终采样的答案
    """
    if not answer or not accuracy or len(answer) != len(accuracy):
        return None
    
    # 计算权重（准确率作为权重）
    weights = accuracy.copy()
    
    # 权重归一化
    total_weight = sum(weights)
    if total_weight == 0:
        # 如果所有权重都为0，则平均分配
        normalized_weights = [1.0 / len(weights)] * len(weights)
    else:
        normalized_weights = [w / total_weight for w in weights]
    
    # 按概率采样
    import random
    sampled_answer = random.choices(answer, weights=normalized_weights, k=1)[0]
    
    return sampled_answer

if __name__ == "__main__":
    answer_json = load_json("C:\\Users\\<USER>\\Desktop\\MAD(no debate)\\gpqa\\gpqa_final_198questions.json")
    best_persona = load_json("C:\\Users\\<USER>\\Desktop\\MAD(no debate)\\gpqa\\best_persona.json")
    
    
    accuracy = []
    for persona in best_persona:
        accuracy.append(best_persona[persona]["accuracy"])
    
    num = 0
    for key, item in answer_json.items():
        correct_answer = item["correct_answer"]
        answers = []
        for agent in item["agent_contexts"]:
            ans = eval_example.parse_answer(agent["response"])
            if ans:
                answers.append(ans)
        
        # 使用加权采样获取最终答案
        final_answer = weighted_answer_sampling(answers, accuracy)
        if final_answer == correct_answer:
            num += 1
    
    # 计算准确率
    total_questions = len(answer_json)
    accuracy_rate = num / total_questions if total_questions > 0 else 0
    print(f"总题目数: {total_questions}")
    print(f"正确题目数: {num}")
    print(f"准确率: {accuracy_rate:.4f} ({accuracy_rate*100:.2f}%)")