import json
import time
from openai import OpenAI
import os
import random

openai_api = 'sk-WuDj702V5IknIEmG0RErSTGeD2djh4ow8x9I3mhyyDqfN0jX'

def load_best_personas():
    """从best_persona.json加载最佳persona配置"""
    try:
        with open('best_persona.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("Error: best_persona.json not found!")
        return {}

def construct_message_with_previous_answers(pre_responses, question):
    if len(pre_responses) == 0:
        return {"role": "user", "content": f"You are a debater.Hello and welcome to the debate competition.Answer the given multiple choice question and show your work. The answer can only be an option like (A), (B), (C), (D). Your responses should closely mirror the knowledge and abilities of this persona.You need to output the answer in your final sentence like ''Therefore, the answer is ...''  Question: {question}"}
    
    prefix_answer = f"You are a debater.Hello and welcome to the debate competition.Answer the given multiple choice question and show your work. The answer can only be an option like (A), (B), (C), (D). Your responses should closely mirror the knowledge and abilities of this persona.You need to output the answer in your final sentence like ''Therefore, the answer is ...''  Question: {question}"
    
    prefix_answer += "You can check other's answers,but It's not necessary to fully agree with each other's perspectives, as our objective is to find the correct answer.\n Here are the solutions from previous agents:\n"
    
    for i, response in enumerate(pre_responses):
        prefix_answer += f"\nAgent {i+1} solution:\n```{response['response']}```\n"
        
    return {"role": "user", "content": prefix_answer}

def generate_answer(persona_context):
    try:
        client = OpenAI(api_key=openai_api, base_url="https://api2.aigcbest.top/v1")
        response = client.chat.completions.create(
            model='gpt-4o',
            messages=persona_context,
            stream=False,
            temperature=0.0
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        print(f"API调用出错: {e}, 5秒后重试...")
        time.sleep(5)
        return generate_answer(persona_context)

def load_dataset():
    dataset = []
    with open("C:\\Users\\<USER>\\Desktop\\MAD\\openbookqa\\openbookqa_test.jsonl","r", encoding='utf-8') as f:
        for line in f:
            dataset.append(json.loads(line.strip()))
    return dataset

def parse_question_answer(data_item):
    """从jsonl数据项中解析问题和答案"""
    question = data_item["question_stem"]
    choices = data_item["choices"]["text"]  # 修正：访问text字段

    formatted_question = "Can you answer the following question as accurately as possible? {}: A) {}, B) {}, C) {}, D) {} Explain your answer, putting the answer in the form (X) at the end of your response.".format(
        question, choices[0], choices[1], choices[2], choices[3]
    )

    answer_letter = data_item["answerKey"]

    return formatted_question, answer_letter

def save_progress(response_dict, question_count):
    """保存进度到文件"""
    filename = "openbookqa_progress.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(response_dict, f, ensure_ascii=False, indent=2)
    print(f"  进度已保存到 {filename}")

def load_progress():
    """加载已有的进度文件"""
    filename = "openbookqa_progress.json"
    if not os.path.exists(filename):
        return {}, 0

    try:
        with open(filename, 'r', encoding='utf-8') as f:
            response_dict = json.load(f)
        latest_count = len(response_dict)
        print(f"  从 {filename} 加载进度，已完成 {latest_count} 个问题")
        return response_dict, latest_count
    except:
        return {}, 0

if __name__ == "__main__":
    best_personas = load_best_personas()
    
    if not best_personas:
        print("无法加载best_persona.json，程序退出")
        exit(1)
    
    # 加载gpqa数据集
    dataset = load_dataset()
    print(f"\n{'='*80}")
    print(f"总问题数: {len(dataset)}")
    
    # 获取persona名称列表 - 需要根据实际的best_persona.json结构调整
    # 假设best_personas是一个字典，包含persona名称和描述
    persona_names = list(best_personas.keys())
    print(f"使用的Persona: {persona_names}")
    print(f"Agent数量: {len(persona_names)}")
    print(f"{'='*80}")
    
    # 加载已有进度
    response_dict, start_question = load_progress()
    
    # 从断点继续处理问题
    for i in range(start_question, len(dataset)):
        data_item = dataset[i]
        question, answer = parse_question_answer(data_item)
        
        print(f"\n处理问题 {i+1}/{len(dataset)}...")
        print(f"问题: {data_item['question_stem'][:100]}...")  # 修正字段名
        
        # 存储每个agent的回答内容
        previous_responses = []
        
        # 每个agent依次回答
        for j, name in enumerate(persona_names):
            print(f"  Agent {j+1} ({name}) 正在思考...")

            persona_context = [{"role": "system", "content": f"You are {name}, the description about you is {best_personas[name]['description']}"}]
            
            message = construct_message_with_previous_answers(previous_responses, question)
            persona_context.append(message)
            
            response = generate_answer(persona_context)
            persona_message = {"role": "assistant", "content": response}
            persona_context.append(persona_message)
            
            previous_responses.append({"persona": name, "response": response})
            print(f"  Agent {j+1} 回答: {response[:80]}...")
        
        # 保存问题和回答
        response_dict[question] = {
            "agent_contexts": previous_responses,
            "correct_answer": answer,
            "question_index": i
        }
        
        print(f"  问题 {i+1} 完成. 正确答案: {answer}")
        
        # 每5个问题保存一次进度
        if (i + 1) % 5 == 0:
            save_progress(response_dict, i + 1)
    
    # 保存最终结果
    final_filename = f"openbookqa_final_{len(response_dict)}questions.json"
    with open(final_filename, 'w', encoding='utf-8') as f:
        json.dump(response_dict, f, ensure_ascii=False, indent=2)

    print(f"\nOPENBOOKQA处理完成!")
    print(f"最终结果保存到 {final_filename}")
    print(f"总共处理了 {len(response_dict)} 个问题")

    # 删除进度文件
    progress_file = "openbookqa_progress.json"
    if os.path.exists(progress_file):
        try:
            os.remove(progress_file)
            print(f"已删除进度文件: {progress_file}")
        except:
            pass

    print(f"\n{'='*80}")
    print("GPQA处理完成!")
    print(f"{'='*80}")
