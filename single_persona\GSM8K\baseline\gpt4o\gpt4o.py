from openai import OpenAI
import json
import time
import re
from multiprocessing import Pool
import os
from time import sleep

openai_api = 'sk-HnEVpMqIHngQGRX4GVKxKgwH79J96S994RO7wMqQVVHYhYMn'
ANS_RE = re.compile(r"#### (\-?[0-9\.\,]+)")
INVALID_ANS = "[invalid]"

def find_last_number_str(s):
    matches = list(re.finditer(r'[-+]?\d+\.?\d*', s))
    q=""
    if matches:
        number = matches[-1].group(0).strip()
        if number.endswith('.'):
            number = number.rstrip('.')
        if number.startswith('+'):
            number = number.lstrip('+')
        match = re.search(".",number)
        if match:
            flag1 = 0
            flag2 = 1
            for i in number:
                if i == '.': flag1=1
                if flag1 and i != '.':
                    if i !='0': flag2 = 0
            if not flag2:
                for i in number:
                    q+=i
            else:
                flag3=0
                for i in number:
                    if i == '.':flag3=1
                    if flag3:continue
                    q+=i
            
            return q
    return "ERROR"

def extract_answer(completion):
    match = ANS_RE.search(completion)
    if match:
        match_str = match.group(1).strip().replace(",", "")
        return match_str
    return INVALID_ANS

def process_gpt4o(item_with_index):
    index, item = item_with_index
    question = item["question"]
    true_answer = extract_answer(item["answer"])

    client = OpenAI(api_key=openai_api, base_url="https://api2.aigcbest.top/v1")

    for attempt in range(3):
        try:
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system",
                     "content": "You are a math expert. Solve the following question step by step, and output the answer.You should output the final answer in the format:The final answer is at the end of output.The question is:"},
                    {"role": "user", "content": question},
                ],
                stream=False,
                temperature=0.0,
                max_tokens=450
            )
            raw_output = response.choices[0].message.content.strip()
            raw_output = raw_output.replace(',','')
            predicted_answer=find_last_number_str(raw_output)
            is_correct = predicted_answer == true_answer
            return {
                "question_number": index + 1,
                "question": question,
                "true_answer": true_answer,
                "predicted_answer": predicted_answer,
                "is_correct": is_correct,
                "raw_output": raw_output
            }
        except Exception as e:
            if attempt < 2:
                sleep(1)
            else:
                return {
                    "question_number": index + 1,
                    "question": question,
                    "true_answer": true_answer,
                    "predicted_answer": "ERROR",
                    "is_correct": False,
                    "error": str(e)
                }


def main():
    # 加载数据集
    dataset = []
    with open("test.jsonl", "r", encoding="utf-8") as f:
        for line in f:
            dataset.append(json.loads(line.strip()))

    num_workers = min(os.cpu_count(), 5)
    total_number = len(dataset)
    tasks = [(i, item) for i, item in enumerate(dataset)]

    start = time.time()
    print(f"Starting processing {total_number} questions with {num_workers} workers...")

    with Pool(processes=num_workers) as pool:
        results = pool.imap(process_gpt4o, tasks)

        right_count = 0
        with open("PREDICTED_result_gpt4o.jsonl", "a", encoding="utf-8") as f:
            for result in results:
                if result["is_correct"]:
                    right_count += 1
                    print(
                        f"Question {result['question_number']}/{total_number}: Predicted={result['predicted_answer']}, True={result['true_answer']}, Correct")
                else:
                    print(
                        f"Question {result['question_number']}/{total_number}: Predicted={result['predicted_answer']}, True={result['true_answer']}, Wrong, Raw={result.get('raw_output', 'N/A')}")
                f.write(json.dumps(result) + "\n")
                f.write("----------------------"+"\n")

    end = time.time()
    accuracy = right_count / total_number
    total_time = end - start
    print(f"\nAll questions processed!")
    print(f"total_time={total_time:.2f} seconds, accuracy={accuracy:.2%}")


if __name__ == "__main__":
    main()