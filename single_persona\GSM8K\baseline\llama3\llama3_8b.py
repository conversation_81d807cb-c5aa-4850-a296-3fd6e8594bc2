import json
import time
import re
from multiprocessing import Pool
import os
from time import sleep
import requests

ollama_url = "http://localhost:11434/api/generate"

ANS_RE = re.compile(r"#### (\-?[0-9\.\,]+)")
INVALID_ANS = "[invalid]"


def find_last_number_str(s):
    matches = list(re.finditer(r'[-+]?\d+\.?\d*', s))
    q=""
    if matches:
        number = matches[-1].group(0).strip()
        if number.endswith('.'):
            number = number.rstrip('.')
        if number.startswith('+'):
            number = number.lstrip('+')
        match = re.search(".",number)
        if match:
            flag1 = 0
            flag2 = 1
            for i in number:
                if i == '.': flag1=1
                if flag1 and i != '.':
                    if i !='0': flag2 = 0
            if not flag2:
                for i in number:
                    q+=i
            else:
                flag3=0
                for i in number:
                    if i == '.':flag3=1
                    if flag3:continue
                    q+=i
            
            return q
    return "ERROR"


def extract_answer(completion):
    match = ANS_RE.search(completion)
    if match:
        match_str = match.group(1).strip().replace(",", "")
        return match_str
    return INVALID_ANS


def process_llama3(item_with_index):
    index, item = item_with_index
    question = item["question"]
    true_answer = extract_answer(item["answer"])

    for attempt in range(3):
        try:
            response = requests.post(
                ollama_url,
                json={
                    "model": "llama3.1:8b",
                    "prompt": (
                        "You are a math expert. Solve the following question step by step, and output the answer.You should output the final answer in the format:The final answer is at the end of output.The question is: " + question
                    ),
                    "stream": False,
                    "temperature": 0.0,
                    "max_token":450
                },
                timeout=30
            )
            response.raise_for_status()
            result = response.json()
            if "response" not in result:
                raise ValueError(f"Invalid JSON: {result}")
            raw_output = result["response"].strip()
            raw_output = raw_output.replace(',','')
            predicted_answer = find_last_number_str(raw_output)
            is_correct = predicted_answer == true_answer
            return {
                "question_number": index + 1,
                "question": question,
                "true_answer": true_answer,
                "predicted_answer": predicted_answer,
                "is_correct": is_correct,
                "raw_output": raw_output
            }
        except Exception as e:
            if attempt < 2:
                sleep(2 ** attempt)  # 指数退避
            else:
                return {
                    "question_number": index + 1,
                    "question": question,
                    "true_answer": true_answer,
                    "predicted_answer": "ERROR",
                    "is_correct": False,
                    "error": f"{type(e).__name__}: {str(e)}",
                    "raw_output":"[INVALID] ERROR"
                }


def main():
    dataset = []
    with open("test.jsonl", "r", encoding="utf-8") as f:
        for line in f:
            dataset.append(json.loads(line.strip()))

    num_workers = min(os.cpu_count(), 5)
    total_number = len(dataset)
    tasks = [(i, item) for i, item in enumerate(dataset)]

    start = time.time()
    print(f"Starting {total_number} questions with {num_workers} workers...")

    with Pool(processes=num_workers) as pool:
        results = pool.imap(process_llama3, tasks)

        right_count = 0
        with open("predict_result_llama3.1_8b_test2.jsonl", "w", encoding="utf-8") as f:
            for i, result in enumerate(results, 1):
                if result["is_correct"]:
                    right_count += 1
                    print(
                        f"{i}. Q{result['question_number']}/{total_number}: {result['predicted_answer']} = {result['true_answer']} (Correct)")
                else:
                    print(
                        f"{i}. Q{result['question_number']}/{total_number}: {result['predicted_answer']} ≠ {result['true_answer']} (Wrong) raw={result['raw_output']}")
                f.write(json.dumps(result) + "\n")
                f.write("-------------"+"\n")

    end = time.time()
    accuracy = right_count / total_number
    total_time = end - start
    print(f"\nDone! Time={total_time:.2f}s, Accuracy={accuracy:.2%}")


if __name__ == "__main__":
    main()
