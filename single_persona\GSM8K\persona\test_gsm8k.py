from openai import OpenAI
import json
import time
import re
from multiprocessing import Pool
import os
from time import sleep

openai_api = 'sk-WuDj702V5IknIEmG0RErSTGeD2djh4ow8x9I3mhyyDqfN0jX'
INVALID_ANS = "[invalid]"

def extract_boxed_answer(completion):
    # 匹配\boxed{answer}格式的答案
    match = re.search(r"\\boxed\{(\-?[0-9\.\,]+)\}", completion)
    if match:
        # 移除所有逗号
        answer = match.group(1).strip().replace(",", "")
        # 检查是否是有效的数字格式
        try:
            float(answer)
            return answer
        except ValueError:
            return INVALID_ANS
    return INVALID_ANS

def extract_answer(completion):
    # 匹配####格式的答案
    match = re.search(r"#### (\-?[0-9\.\,]+)", completion)
    if match:
        return match.group(1).strip().replace(",", "")
    return INVALID_ANS

def normalize_number(num_str):
    try:
        # 移除所有逗号
        num_str = num_str.replace(',', '')
        # 转换为浮点数
        num = float(num_str)
        # 如果是整数，返回整数形式
        if num.is_integer():
            return str(int(num))
        # 否则返回原始数字字符串
        return str(num)
    except:
        return num_str

def process_with_persona(item_with_index):
    index, item, persona = item_with_index
    question = item["question"]
    true_answer = extract_answer(item["answer"])
    true_answer = normalize_number(true_answer)

    client = OpenAI(api_key=openai_api, base_url="https://api2.aigcbest.top/v1")

    for attempt in range(3):
        try:
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system",
                     "content": f"""As {persona['name']}, {persona['description']} 
Can you solve the following math problem? Explain your reasoning. Your final answer should be a single numerical number, in the form \\boxed{{answer}}, at the end of your response."""},
                    {"role": "user", "content": question},
                ],
                stream=False,
                temperature=0.0
            )
            raw_output = response.choices[0].message.content.strip()
            predicted_answer = extract_boxed_answer(raw_output)
            
            predicted_answer = normalize_number(predicted_answer)
            is_correct = predicted_answer == true_answer
            return {
                "question_number": index + 1,
                "question": question,
                "true_answer": true_answer,
                "predicted_answer": predicted_answer,
                "is_correct": is_correct,
                "raw_output": raw_output,
                "persona": persona['name']
            }
        except Exception as e:
            if attempt < 2:
                sleep(1)
            else:
                return {
                    "question_number": index + 1,
                    "question": question,
                    "true_answer": true_answer,
                    "predicted_answer": "ERROR",
                    "is_correct": False,
                    "error": str(e),
                    "persona": persona['name']
                }

def load_personas():
    with open("persona.json", "r", encoding="utf-8") as f:
        data = json.load(f)
    # 返回分类信息和persona列表
    return data

def main():
    # 加载数据集
    dataset = []
    with open("test.jsonl", "r", encoding="utf-8") as f:
        for line in f:
            dataset.append(json.loads(line.strip()))

    # 加载所有persona（按分类）
    categories = load_personas()
    print(f"\n总共加载了 {len(categories)} 个分类")
    
    num_workers = min(os.cpu_count(), 5)
    total_number = len(dataset)  # 使用全部数据
    print(f"总共加载了 {total_number} 道题目")

    # 尝试加载已有的结果
    all_results = {}
    if os.path.exists("all_persona_results.json"):
        with open("all_persona_results.json", "r", encoding="utf-8") as f:
            all_results = json.load(f)
        print(f"\n已加载已有的测试结果")

    # 遍历每个分类
    for category in categories:
        category_name = category["category"]
        personas = category["personas"]
        
        # 如果这个分类还没有结果，初始化它
        if category_name not in all_results:
            all_results[category_name] = {}
        
        # 过滤掉已经测试过的persona
        remaining_personas = [p for p in personas if p['name'] not in all_results[category_name]]
        print(f"\n分类 '{category_name}' 剩余 {len(remaining_personas)} 个persona待测试")

        for persona in remaining_personas:
            print(f"\n开始测试 persona: {persona['name']}")
            print("-" * 50)
            
            tasks = [(i, item, persona) for i, item in enumerate(dataset)]

            start = time.time()
            print(f"正在处理 {total_number} 道题目...")

            with Pool(processes=num_workers) as pool:
                results = pool.imap(process_with_persona, tasks)

                right_count = 0
                correct_questions = []  # 存储当前persona答对的题目编号
                total_processed = 0  # 记录总处理数量
                for result in results:
                    if result["is_correct"]:
                        right_count += 1
                        correct_questions.append(result["question_number"])
                    total_processed += 1
                    # 每个问题只输出编号、预测结果和是否正确
                    print(f"Q{result['question_number']}: {result['predicted_answer']} ({'✓' if result['is_correct'] else '✗'})")
                    if total_processed % 100 == 0:  # 每100个问题输出一次进度
                        print(f"\n进度: {total_processed}/{total_number}, 当前正确率: {right_count/total_processed:.2%}\n")

            end = time.time()
            accuracy = right_count / total_number
            
            # 保存当前persona的结果到对应分类下
            all_results[category_name][persona['name']] = {
                "accuracy": accuracy,
                "correct_count": right_count,
                "correct_questions": correct_questions
            }

            # 每完成一个persona就保存一次结果
            with open("all_persona_results.json", "w", encoding="utf-8") as f:
                json.dump(all_results, f, ensure_ascii=False, indent=2)

            print(f"\n{persona['name']} 测试完成！")
            print(f"正确率: {accuracy:.2%}")
            print(f"答对题目数: {right_count}/{total_number}")
            print("=" * 50)

    # 打印总结
    print("\n所有persona测试完成！")
    print("\n各分类准确率排名：")
    for category_name, category_results in all_results.items():
        print(f"\n{category_name}:")
        sorted_results = sorted(category_results.items(), key=lambda x: x[1]["accuracy"], reverse=True)
        for persona, result in sorted_results:
            print(f"  {persona}: {result['accuracy']:.2%}")

if __name__ == "__main__":
    main()
    
    
