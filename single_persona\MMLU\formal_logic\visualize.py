import json
import matplotlib.pyplot as plt
import numpy as np
from collections import Counter
import seaborn as sns

def load_results():
    with open("all_persona_results.json", "r", encoding="utf-8") as f:
        return json.load(f)

def plot_accuracy_comparison(results):
    # 计算每个persona的准确率
    accuracies = {}
    total_questions = 1319  # GSM8K测试集的总题目数
    
    # 遍历每个分类
    for category, personas in results.items():
        for persona, data in personas.items():
            accuracies[f"{category}-{persona}"] = data["accuracy"]
    
    # 按准确率排序
    sorted_accuracies = dict(sorted(accuracies.items(), key=lambda x: x[1], reverse=True))
    
    # 创建图表
    plt.figure(figsize=(15, 8))
    plt.bar(range(len(sorted_accuracies)), list(sorted_accuracies.values()))
    plt.xticks(range(len(sorted_accuracies)), list(sorted_accuracies.keys()), rotation=45, ha='right')
    plt.title('各Persona在mmlu_college_mathematics数据集上的准确率比较')
    plt.xlabel('Persona')
    plt.ylabel('准确率')
    plt.tight_layout()
    plt.savefig('accuracy_comparison.png')
    plt.close()

def plot_category_comparison(results):
    # 计算每个分类的平均准确率
    category_accuracies = {}
    for category, personas in results.items():
        accuracies = [data["accuracy"] for data in personas.values()]
        category_accuracies[category] = np.mean(accuracies)
    
    # 按准确率排序
    sorted_categories = dict(sorted(category_accuracies.items(), key=lambda x: x[1], reverse=True))
    
    # 创建图表
    plt.figure(figsize=(10, 6))
    plt.bar(range(len(sorted_categories)), list(sorted_categories.values()))
    plt.xticks(range(len(sorted_categories)), list(sorted_categories.keys()), rotation=45, ha='right')
    plt.title('各分类在mmlu_college_mathematics数据集上的平均准确率比较')
    plt.xlabel('分类')
    plt.ylabel('平均准确率')
    plt.tight_layout()
    plt.savefig('category_comparison.png')
    plt.close()

def plot_question_overlap(results):
    # 统计每个题目被多少个persona答对
    question_counts = Counter()
    for category, personas in results.items():
        for persona, data in personas.items():
            question_counts.update(data["correct_questions"])
    
    # 创建直方图
    plt.figure(figsize=(10, 6))
    plt.hist(list(question_counts.values()), bins=range(len(results) + 2))
    plt.title('题目被答对的次数分布')
    plt.xlabel('被答对的次数')
    plt.ylabel('题目数量')
    plt.savefig('question_overlap.png')
    plt.close()

def plot_heatmap(results):
    # 获取所有题目编号
    all_questions = set()
    for category, personas in results.items():
        for persona, data in personas.items():
            all_questions.update(data["correct_questions"])
    all_questions = sorted(list(all_questions))
    
    # 创建热力图数据
    heatmap_data = np.zeros((len(results), len(all_questions)))
    for i, (category, personas) in enumerate(results.items()):
        for persona, data in personas.items():
            for question in data["correct_questions"]:
                heatmap_data[i, all_questions.index(question)] = 1
    
    # 绘制热力图
    plt.figure(figsize=(20, 10))
    sns.heatmap(heatmap_data, cmap='YlOrRd', 
                xticklabels=False, yticklabels=list(results.keys()))
    plt.title('Persona答题情况热力图')
    plt.xlabel('题目编号')
    plt.ylabel('分类')
    plt.tight_layout()
    plt.savefig('answer_heatmap.png')
    plt.close()

def plot_category_distribution(results):
    # 统计每个分类的persona数量
    category_counts = {category: len(personas) for category, personas in results.items()}
    
    # 创建饼图
    plt.figure(figsize=(10, 10))
    plt.pie(category_counts.values(), labels=category_counts.keys(), autopct='%1.1f%%')
    plt.title('各分类Persona数量分布')
    plt.savefig('category_distribution.png')
    plt.close()

def main():
    # 加载结果
    results = load_results()
    
    # 生成可视化
    plot_accuracy_comparison(results)
    plot_category_comparison(results)
    plot_question_overlap(results)
    plot_heatmap(results)
    plot_category_distribution(results)
    
    print("可视化结果已保存为：")
    print("1. accuracy_comparison.png - 各Persona准确率比较")
    print("2. category_comparison.png - 各分类平均准确率比较")
    print("3. question_overlap.png - 题目被答对次数分布")
    print("4. answer_heatmap.png - Persona答题情况热力图")
    print("5. category_distribution.png - 各分类Persona数量分布")

if __name__ == "__main__":
    main() 