Stack trace:
Frame         Function      Args
0007FFFF8E80  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFF8E80, 0007FFFF7D80) msys-2.0.dll+0x1FEBA
0007FFFF8E80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9158) msys-2.0.dll+0x67F9
0007FFFF8E80  000210046832 (000210285FF9, 0007FFFF8D38, 0007FFFF8E80, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E80  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF8E80  0002100690B4 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9160  00021006A49D (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFEC8660000 ntdll.dll
7FFEC6950000 KERNEL32.DLL
7FFEC58E0000 KERNELBASE.dll
7FFEC6600000 USER32.dll
7FFEC6040000 win32u.dll
000210040000 msys-2.0.dll
7FFEC6A20000 GDI32.dll
7FFEC5F00000 gdi32full.dll
7FFEC5CD0000 msvcp_win.dll
7FFEC61F0000 ucrtbase.dll
7FFEC76F0000 advapi32.dll
7FFEC7640000 msvcrt.dll
7FFEC7C40000 sechost.dll
7FFEC71B0000 RPCRT4.dll
7FFEC4ED0000 CRYPTBASE.DLL
7FFEC57B0000 bcryptPrimitives.dll
7FFEC7E60000 IMM32.DLL
